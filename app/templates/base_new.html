{% from 'custom_macros.html' import render_flash_messages, render_ajax_alert_container %}
<!DOCTYPE html>
<html lang="{{ g.lang_code }}" data-theme="{{ g.theme }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FiestaMagic{% endblock %}</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --bs-primary: #6366f1;
            --bs-primary-rgb: 99, 102, 241;
            --bs-link-color: #6366f1;
            --bs-link-hover-color: #4f46e5;
        }

        /* Theme variables */
        html[data-theme="light"] {
            --bg-color: #f8fafc;
            --text-color: #1e293b;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
        }

        html[data-theme="dark"] {
            --bg-color: #0f172a;
            --text-color: #e2e8f0;
            --card-bg: #1e293b;
            --border-color: #334155;
        }

        /* Apply theme */
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .card, .modal-content, .dropdown-menu {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        /* Logo switching based on theme */
        html[data-theme="light"] .logo-light { display: inline-block !important; }
        html[data-theme="light"] .logo-dark { display: none !important; }
        html[data-theme="dark"] .logo-light { display: none !important; }
        html[data-theme="dark"] .logo-dark { display: inline-block !important; }

        /* Default state (before JS loads) */
        .logo-light { display: inline-block; }
        .logo-dark { display: none; }

        /* Toast positioning */
        .toast-container {
            z-index: 1100;
            pointer-events: none; /* Allow clicks to pass through the container */
        }

        .toast {
            pointer-events: auto; /* But make the toasts themselves clickable */
            margin-bottom: 0.5rem;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            opacity: 0;
            transition: opacity 0.15s ease-out;
        }

        .toast.show {
            opacity: 1;
        }

        .toast.hide {
            opacity: 0;
        }

        /* Prevent multiple animations */
        .toast.fade {
            transition: opacity 0.15s ease-out !important;
        }

        /* Footer always at bottom */
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main {
            flex: 1;
        }

        /* Improved form styling */
        .form-control:focus, .form-select:focus, .form-check-input:focus {
            border-color: #a5b4fc;
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        .btn-primary {
            background-color: #6366f1;
            border-color: #6366f1;
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }

        .btn-outline-primary {
            color: #6366f1;
            border-color: #6366f1;
        }

        .btn-outline-primary:hover, .btn-outline-primary:focus {
            background-color: #6366f1;
            border-color: #6366f1;
        }

        /* Improved card styling */
        .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border-radius: 0.5rem;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }

        /* Improved navbar */
        .navbar-brand {
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        /* Breadcrumbs styling */
        .breadcrumb {
            background-color: transparent;
            padding: 0.75rem 0;
            margin-bottom: 1rem;
        }

        /* Custom button focus */
        .btn:focus {
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        /* Improved form validation feedback */
        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        /* Improved alerts */
        .alert {
            border-radius: 0.5rem;
        }

        /* Improved modal styling */
        .modal-content {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .modal-header, .modal-footer {
            border-color: var(--border-color);
        }

        /* Improved dropdown styling */
        .dropdown-menu {
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }

        .dropdown-item:hover, .dropdown-item:focus {
            background-color: rgba(99, 102, 241, 0.1);
        }

        /* Improved table styling */
        .table {
            border-color: var(--border-color);
        }

        /* Improved pagination */
        .page-link {
            color: #6366f1;
            border-color: var(--border-color);
        }

        .page-link:hover {
            color: #4f46e5;
            background-color: rgba(99, 102, 241, 0.1);
            border-color: var(--border-color);
        }

        .page-item.active .page-link {
            background-color: #6366f1;
            border-color: #6366f1;
        }
    </style>

    {% block styles %}{% endblock %}
</head>

<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <img src="{{ url_for('static', filename='logo-light.svg') }}" alt="FiestaMagic" height="30" class="logo-light">
                <img src="{{ url_for('static', filename='logo-dark.svg') }}" alt="FiestaMagic" height="30" class="logo-dark">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">{{ _('Home') }}</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="themeDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-palette me-1"></i>{{ _('Theme') }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="themeDropdown">
                            <li><button class="dropdown-item" onclick="setTheme('light')"><i
                                        class="bi bi-sun me-2"></i>{{ _('Light') }}</button></li>
                            <li><button class="dropdown-item" onclick="setTheme('dark')"><i
                                        class="bi bi-moon me-2"></i>{{ _('Dark') }}</button></li>
                            <li><button class="dropdown-item" onclick="setTheme('auto')"><i
                                        class="bi bi-circle-half me-2"></i>{{ _('OS Default') }}</button></li>
                        </ul>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="{{ current_user.gravatar(size=24) }}" alt="{{ current_user.username }}" class="rounded-circle me-1">
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}"><i
                                        class="bi bi-person me-2"></i>{{ _('Profile') }}</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i
                                        class="bi bi-box-arrow-right me-2"></i>{{ _('Logout') }}</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">{{ _('Login') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">{{ _('Register') }}</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                {% block breadcrumbs %}
                <li class="breadcrumb-item active" aria-current="page">{{ _('Home') }}</li>
                {% endblock %}
            </ol>
        </nav>
    </div>

    <!-- Main content -->
    <main class="py-4">
        <div class="container">
            <!-- Flash messages -->
            {{ render_flash_messages() }}
            {{ render_ajax_alert_container() }}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="py-3 mt-4 border-top">
        <div class="container">
            <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center">
                <div class="mb-2 mb-sm-0">
                    <span>&copy; 2023 FiestaMagic. {{ _('All rights reserved') }}.</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2">{{ _('Current language') }}:</span>
                    <a href="{{ url_for('main.set_language', lang='en', next=request.path) }}" class="me-2 {% if g.lang_code == 'en' %}fw-bold{% endif %}">English</a>
                    <a href="{{ url_for('main.set_language', lang='pl', next=request.path) }}" class="{% if g.lang_code == 'pl' %}fw-bold{% endif %}">Polski</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Theme handling -->
    <script>
        function setTheme(theme) {
            if (theme === 'auto') {
                localStorage.removeItem('theme');
                applyTheme(getPreferredTheme());
            } else {
                localStorage.setItem('theme', theme);
                applyTheme(theme);
            }
        }

        function getPreferredTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                return savedTheme;
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        function applyTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
        }

        // Apply theme on page load
        document.addEventListener('DOMContentLoaded', () => {
            applyTheme(getPreferredTheme());

            // Listen for OS theme changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                if (!localStorage.getItem('theme')) {
                    applyTheme(getPreferredTheme());
                }
            });
        });
    </script>

    <!-- Common UI enhancements -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize all tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })

            // Initialize all toasts
            var toastElList = [].slice.call(document.querySelectorAll('.toast'))
            var toastList = toastElList.map(function (toastEl) {
                var toast = new bootstrap.Toast(toastEl, {
                    autohide: true,
                    delay: 500 // Ultra brief display (0.5 seconds)
                })
                toast.show()
                return toast
            })

            // Setup AJAX form submissions for reservation actions
            setupAjaxForms();
        });

        // Function to show a toast alert
        function showToast(message, type = 'success') {
            // Check if we already have a toast with the same message to prevent duplicates
            const existingToasts = document.querySelectorAll('.toast-body');
            for (let i = 0; i < existingToasts.length; i++) {
                if (existingToasts[i].textContent.trim() === message.trim()) {
                    return; // Don't create duplicate toasts
                }
            }

            const iconClass = type === 'success' ? 'bi-check-circle' :
                type === 'danger' ? 'bi-exclamation-triangle' :
                    type === 'warning' ? 'bi-exclamation-circle' : 'bi-info-circle';

            // Create a single toast element
            const toastElement = document.createElement('div');
            toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');

            toastElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body py-2">
                        <i class="bi ${iconClass} me-1"></i>
                        ${message}
                    </div>
                </div>
            `;

            const toastContainer = document.getElementById('ajax-toast-container');
            toastContainer.appendChild(toastElement);

            // Initialize and show the toast with a very brief delay
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 500 // Ultra brief display (0.5 seconds)
            });

            // Use setTimeout to ensure smooth animation
            setTimeout(() => {
                toast.show();
            }, 10);

            // Remove the toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }

        // Function to setup AJAX form submissions
        function setupAjaxForms() {
            // Find all reservation forms
            document.querySelectorAll('form[action*="toggle_reserved"], .reservation-form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get the form data
                    const formData = new FormData(form);

                    // Get the button that was clicked
                    const button = form.querySelector('button[type="submit"]');
                    const originalHtml = button.innerHTML;

                    // Show loading state
                    button.disabled = true;
                    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

                    // Send the form data via fetch
                    fetch(form.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': formData.get('csrf_token')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Show success message
                        showToast(data.message, data.category || 'success');

                        // Update the UI based on the new reservation status
                        if (data.reserved !== undefined) {
                            updateReservationUI(form, data.reserved, data.gift_id);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('An error occurred. Please try again.', 'danger');
                    })
                    .finally(() => {
                        // Restore button state
                        button.disabled = false;
                        button.innerHTML = originalHtml;
                    });
                });
            });

            // Find all privacy toggle forms
            document.querySelectorAll('form[action*="toggle_privacy"]').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get the form data
                    const formData = new FormData(form);

                    // Get the button that was clicked
                    const button = form.querySelector('button[type="submit"]');
                    const originalHtml = button.innerHTML;

                    // Show loading state
                    button.disabled = true;
                    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

                    // Send the form data via fetch
                    fetch(form.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': formData.get('csrf_token')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Show success message
                        showToast(data.message, data.category || 'success');

                        // Update the UI based on the new privacy status
                        if (data.is_private !== undefined) {
                            updatePrivacyUI(data.is_private);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('An error occurred. Please try again.', 'danger');
                    })
                    .finally(() => {
                        // Restore button state
                        button.disabled = false;
                        button.innerHTML = originalHtml;
                    });
                });
            });
        }

        // Function to update the UI based on reservation status
        function updateReservationUI(form, isReserved, giftId) {
            // Find the gift card containing this form
            const giftCard = form.closest('.card');
            if (!giftCard) return;

            // Update card border
            if (isReserved) {
                giftCard.classList.add('border-success');
            } else {
                giftCard.classList.remove('border-success');
            }

            // Update gift title (strikethrough if reserved)
            const giftTitle = giftCard.querySelector('.card-title');
            if (giftTitle) {
                if (isReserved) {
                    giftTitle.classList.add('text-decoration-line-through');
                } else {
                    giftTitle.classList.remove('text-decoration-line-through');
                }
            }

            // Update reserved badge
            let reservedBadge = giftCard.querySelector('.badge.bg-success');
            if (isReserved) {
                if (!reservedBadge) {
                    // Create badge if it doesn't exist
                    const cardBody = giftCard.querySelector('.card-body');
                    if (cardBody) {
                        const badge = document.createElement('div');
                        badge.className = 'badge bg-success mb-3';
                        badge.textContent = "{{ _('Reserved') }}";
                        cardBody.appendChild(badge);
                    }
                }
            } else {
                // Remove badge if it exists
                if (reservedBadge) {
                    reservedBadge.remove();
                }
            }

            // Update button appearance
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                if (isReserved) {
                    button.className = 'btn btn-sm btn-outline-success';
                    button.setAttribute('data-bs-title', "{{ _('Unreserve') }}");
                    button.innerHTML = '<i class="bi bi-bookmark-check-fill"></i>';
                } else {
                    button.className = 'btn btn-sm btn-outline-warning';
                    button.setAttribute('data-bs-title', "{{ _('Reserve') }}");
                    button.innerHTML = '<i class="bi bi-bookmark"></i>';
                }

                // Reinitialize tooltip
                var tooltip = bootstrap.Tooltip.getInstance(button);
                if (tooltip) {
                    tooltip.dispose();
                }
                new bootstrap.Tooltip(button);
            }
        }

        // Function to update the UI based on privacy status
        function updatePrivacyUI(isPrivate) {
            // Update privacy badge
            const privacyBadge = document.querySelector('.badge[class*="bg-danger"], .badge[class*="bg-success"]');
            if (privacyBadge) {
                if (isPrivate) {
                    privacyBadge.className = 'badge bg-danger me-2';
                    privacyBadge.innerHTML = '<i class="bi bi-lock-fill me-1"></i>{{ _("Private") }}';
                } else {
                    privacyBadge.className = 'badge bg-success me-2';
                    privacyBadge.innerHTML = '<i class="bi bi-unlock-fill me-1"></i>{{ _("Public") }}';
                }
            }

            // Update privacy toggle button
            const privacyButton = document.querySelector('form[action*="toggle_privacy"] button');
            if (privacyButton) {
                if (isPrivate) {
                    privacyButton.className = 'btn btn-danger';
                    privacyButton.innerHTML = '<i class="bi bi-lock me-1"></i>{{ _("Private") }}';
                } else {
                    privacyButton.className = 'btn btn-success';
                    privacyButton.innerHTML = '<i class="bi bi-unlock me-1"></i>{{ _("Public") }}';
                }
            }
        }
    </script>

    {% block scripts %}{% endblock %}
</body>

</html>
