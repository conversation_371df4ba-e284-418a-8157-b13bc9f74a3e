{% extends 'base.html' %}

{% block title %}{{ _('Add Gift') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}">{{
        gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Add Gift') }}</li>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h2 class="mb-0 fs-4">{{ _('Add Gift to') }} "{{ gift_list.name }}"</h2>
                </div>
                <div class="card-body">
                    <form id="addGiftForm" method="POST" action="{{ url_for('main.add_gift', list_id=gift_list.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-3">
                            <label for="link" class="form-label">{{ _('Gift Link') }} <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="link" name="link" required
                                placeholder="{{ _('https://example.com/product') }}">
                            <div class="form-text" id="linkHelpText">{{ _('Enter the URL of the gift you want to add') }}</div>
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">{{ _('Gift Name') }}</label>
                            <input type="text" class="form-control" id="name" name="name"
                                placeholder="{{ _('Leave blank to use title from link') }}">
                            <div class="form-text">{{ _("If left blank, we'll try to extract the title from the link") }}</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.gift_list_view', list_id=gift_list.id) }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>{{ _('Back to List') }}
                            </a>
                            <button type="submit" id="submitButton" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>{{ _('Add Gift') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const linkInput = document.getElementById('link');
        const nameInput = document.getElementById('name');
        const form = document.getElementById('addGiftForm');
        const submitButton = document.getElementById('submitButton');
        const formText = document.getElementById('linkHelpText'); // Get by ID instead of DOM traversal

        // Store the original help text if the element exists
        const originalText = formText ? formText.textContent : '';

        // Track URL changes
        let lastUrl = '';
        let userEnteredName = false;

        // Debounce function to limit how often a function can be called
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }

        // Function to check if a URL is completely different from another
        function isNewUrl(oldUrl, newUrl) {
            if (!oldUrl || !newUrl) return false;

            try {
                // Extract domains for comparison
                const oldDomain = new URL(oldUrl).hostname;
                const newDomain = new URL(newUrl).hostname;

                // If domains are different, it's a completely new URL
                return oldDomain !== newDomain;
            } catch (e) {
                // If URL parsing fails, fall back to simple comparison
                return oldUrl !== newUrl;
            }
        }

        // Function to fetch title from URL
        function fetchTitleFromUrl(url, formTextElement, originalTextContent) {
            // Validate URL format first
            if (!url || !url.match(/^https?:\/\/.+\..+/)) {
                return; // Not a valid URL format, don't try to fetch
            }

            // Show loading indicator if formText exists
            if (formTextElement) {
                formTextElement.innerHTML = '<i class="bi bi-arrow-repeat spinner me-1"></i> {{ _("Fetching title from URL...") }}';
            }

            // Add a timeout to cancel the fetch if it takes too long
            const timeoutId = setTimeout(() => {
                if (formTextElement) {
                    formTextElement.innerHTML = '<i class="bi bi-exclamation-triangle text-warning me-1"></i> {{ _("Title fetch timed out. You can enter a name manually.") }}';
                    setTimeout(() => {
                        if (originalTextContent) formTextElement.textContent = originalTextContent;
                    }, 3000);
                }
            }, 15000); // 15 second timeout

            fetch("{{ url_for('main.get_title') }}?url=" + encodeURIComponent(url))
                .then(response => {
                    clearTimeout(timeoutId);
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Only update name if user hasn't manually entered a name
                    if (!userEnteredName) {
                        nameInput.value = data.title;
                        // Highlight the field with the fetched title
                        nameInput.classList.add('border-success');
                        setTimeout(() => nameInput.classList.remove('border-success'), 2000);
                    }

                    // Restore original help text if formText exists
                    if (formTextElement && originalTextContent) {
                        formTextElement.textContent = originalTextContent;
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('Error:', error);
                    if (formTextElement) {
                        formTextElement.innerHTML = '<i class="bi bi-exclamation-triangle text-warning me-1"></i> {{ _("Could not fetch title. Please enter a name manually.") }}';
                        setTimeout(() => {
                            if (originalTextContent) formTextElement.textContent = originalTextContent;
                        }, 3000);
                    }
                });
        }

        // Create a debounced version of the fetch function (wait 800ms after typing stops)
        const debouncedFetchTitle = debounce(function() {
            const url = linkInput.value;

            // Check if URL has been completely replaced
            const isUrlChanged = isNewUrl(lastUrl, url);

            // Update last URL
            lastUrl = url;

            // Only fetch if name field is empty or URL has completely changed
            if (url && (nameInput.value === '' || isUrlChanged)) {
                // If URL has completely changed, reset userEnteredName flag
                if (isUrlChanged) {
                    userEnteredName = false;
                }

                fetchTitleFromUrl(url, formText, originalText);
            }
        }, 800);

        // Auto-fetch title as user types in the URL field
        linkInput.addEventListener('input', function() {
            debouncedFetchTitle();
        });

        // Immediately fetch title when a URL is pasted
        linkInput.addEventListener('paste', function(e) {
            // Use a shorter timeout for paste events to provide faster feedback
            setTimeout(() => {
                const url = this.value;

                // Check if URL has been completely replaced
                const isUrlChanged = isNewUrl(lastUrl, url);

                // Update last URL
                lastUrl = url;

                // If URL has completely changed, reset userEnteredName flag
                if (isUrlChanged) {
                    userEnteredName = false;
                }

                if (url && (nameInput.value === '' || isUrlChanged)) {
                    fetchTitleFromUrl(url, formText, originalText);
                }
            }, 100); // Short delay to ensure the pasted content is available
        });

        // Also fetch on blur for immediate feedback if needed
        linkInput.addEventListener('blur', function() {
            const url = this.value;
            if (url && nameInput.value === '') {
                fetchTitleFromUrl(url, formText, originalText);
            }
        });

        // Track when user manually enters a name
        nameInput.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                userEnteredName = true;
            } else {
                userEnteredName = false;
                // If name field is cleared and we have a URL, fetch title again
                const url = linkInput.value;
                if (url) {
                    fetchTitleFromUrl(url, formText, originalText);
                }
            }
        });

        // Form submission with loading state
        form.addEventListener('submit', function (e) {
            // Basic validation
            if (!linkInput.value) {
                linkInput.classList.add('is-invalid');
                e.preventDefault();
                return false;
            }

            // Show loading state
            submitButton.disabled = true;
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{{ _("Adding...") }}';

            // Continue with form submission
            return true;
        });

        // Remove validation styling when user starts typing
        linkInput.addEventListener('input', function () {
            this.classList.remove('is-invalid');
        });
    });
</script>
<style>
    /* Simple spinner animation */
    .spinner {
        display: inline-block;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
</style>
{% endblock %}
