{% extends "base.html" %}

{% block title %}{{ _('Edit List') }} - {{ gift_list.name }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift_list.id) }}">{{ gift_list.name
        }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Edit') }}</li>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pencil-square me-2"></i>{{ _('Edit Gift List') }}
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ url_for('main.edit_list', list_id=gift_list.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="mb-4">
                            <label for="name" class="form-label">{{ _('List Name:') }}</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text"><i class="bi bi-list-check"></i></span>
                                <input type="text" class="form-control form-control-lg" id="name" name="name"
                                    value="{{ gift_list.name }}" placeholder="{{ _('Enter a descriptive name') }}"
                                    required>
                            </div>
                            <div class="form-text">{{ _('Give your list a meaningful name like "Birthday Wishlist" or
                                "Wedding Registry"') }}</div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">{{ _('Privacy Setting:') }}</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="is_private" id="privateList"
                                    value="true" {% if gift_list.is_private %}checked{% endif %}>
                                <label class="form-check-label" for="privateList">
                                    <i class="bi bi-lock-fill text-danger me-1"></i>{{ _('Private') }}
                                    <div class="form-text">{{ _('Only you and people with the share link can view this
                                        list') }}</div>
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="is_private" id="publicList"
                                    value="false" {% if not gift_list.is_private %}checked{% endif %}>
                                <label class="form-check-label" for="publicList">
                                    <i class="bi bi-unlock-fill text-success me-1"></i>{{ _('Public') }}
                                    <div class="form-text">{{ _('Anyone can view this list without requiring a share
                                        link') }}</div>
                                </label>
                            </div>
                        </div>

                        {% if gift_list.is_private and gift_list.share_code %}
                        <div class="mb-4">
                            <label class="form-label">{{ _('Share Code:') }}</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text"><i class="bi bi-key"></i></span>
                                <input type="text" class="form-control" value="{{ gift_list.share_code }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyShareCode()">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                            <div class="form-text">{{ _('This code is used in the share link for private lists') }}
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('main.gift_list_view', list_id=gift_list.id) }}"
                                class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>{{ _('Cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitButton">
                                <i class="bi bi-check-lg me-1"></i>{{ _('Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Form submission with loading state
        const form = document.querySelector('form');
        const submitButton = document.getElementById('submitButton');

        if (form && submitButton) {
            form.addEventListener('submit', function () {
                // Show loading state
                submitButton.disabled = true;
                const originalButtonText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{{ _('Saving...') }}';
        });
        }
    });

    function copyShareCode() {
        const codeInput = document.querySelector('input[readonly]');
        codeInput.select();
        document.execCommand('copy');

        // Show feedback
        const button = document.querySelector('.input-group button');
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalHtml;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }
</script>
<style>
    /* Simple spinner animation */
    .spinner-border {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 0.15em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border .75s linear infinite;
    }

    @keyframes spinner-border {
        to {
            transform: rotate(360deg);
        }
    }
</style>
{% endblock %}