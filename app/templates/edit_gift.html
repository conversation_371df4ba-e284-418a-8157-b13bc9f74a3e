{% extends "base.html" %}

{% block title %}{{ _('Edit Gift') }} - {{ gift.name }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}">{{
        gift.gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Edit Gift') }}</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">{{ _('Edit Gift') }}</h1>
    <a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-1"></i> {{ _('Back to List') }}
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-transparent">
        <h5 class="mb-0"><i class="bi bi-pencil-square me-2"></i>{{ _('Edit Gift Details') }}</h5>
    </div>
    <div class="card-body p-4">
        <form method="POST" action="{{ url_for('main.edit_gift', gift_id=gift.id) }}" id="editGiftForm">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="mb-4">
                <label for="name" class="form-label">{{ _('Gift Name:') }}</label>
                <div class="input-group mb-2">
                    <span class="input-group-text"><i class="bi bi-tag"></i></span>
                    <input type="text" class="form-control form-control-lg" id="name" name="name" value="{{ gift.name }}" required>
                </div>
            </div>

            <div class="mb-4">
                <label for="link" class="form-label">{{ _('Gift Link:') }}</label>
                <div class="input-group mb-2">
                    <span class="input-group-text"><i class="bi bi-link"></i></span>
                    <input type="url" class="form-control form-control-lg" id="link" name="link" value="{{ gift.link }}" required>
                </div>
                <div class="form-text" id="linkHelpText">{{ _('The URL where this gift can be purchased') }}</div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{{ url_for('main.gift_list_view', list_id=gift.gift_list_id) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x me-1"></i>{{ _('Cancel') }}
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitButton">
                        <i class="bi bi-check-lg me-1"></i>{{ _('Save Changes') }}
                    </button>
                </div>
                </form>
                </div>
                </div>

                <div class="card mt-4 border-warning shadow-sm">
                    <div class="card-header bg-warning bg-opacity-10">
                        <h5 class="mb-0 text-warning"><i class="bi bi-exclamation-triangle me-2"></i>{{ _('Gift Status') }}</h5>
                    </div>
                    <div class="card-body p-4">
                        <p>{{ _('This gift is currently') }} <strong>{% if gift.reserved %}{{ _('reserved') }}{% else %}{{ _('not reserved')
                                }}{% endif %}</strong>.</p>
                        <form method="POST" action="{{ url_for('main.toggle_reserved', gift_id=gift.id) }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit"
                                class="btn {{ 'btn-outline-success' if not gift.reserved else 'btn-outline-warning' }}">
                                <i class="bi {{ 'bi-bookmark' if not gift.reserved else 'bi-bookmark-dash' }} me-1"></i>
                                {{ _('Mark as Reserved') if not gift.reserved else _('Remove Reservation') }}
                            </button>
                        </form>
    </div>
    </div>
    {% endblock %}

    {% block scripts %}
    {{ super() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('editGiftForm');
            const submitButton = document.getElementById('submitButton');
            const nameInput = document.getElementById('name');
            const linkInput = document.getElementById('link');
            const formText = document.getElementById('linkHelpText'); // Get by ID instead of DOM traversal
            
            // Store original link value to detect changes
            const originalLink = linkInput.value;
            const originalName = nameInput.value;
            
            // Store the original help text if the element exists
            const originalText = formText ? formText.textContent : '';
            
            // Track if the URL has been completely replaced (not just modified)
            let urlCompletelyChanged = false;
            let lastUrl = originalLink;
            
            // Debounce function to limit how often a function can be called
            function debounce(func, wait) {
                let timeout;
                return function(...args) {
                    const context = this;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(context, args), wait);
                };
            }
            
            // Function to check if a URL is completely different from another
            function isNewUrl(oldUrl, newUrl) {
                if (!oldUrl || !newUrl) return false;
                
                try {
                    // Extract domains for comparison
                    const oldDomain = new URL(oldUrl).hostname;
                    const newDomain = new URL(newUrl).hostname;
                    
                    // If domains are different, it's a completely new URL
                    return oldDomain !== newDomain;
                } catch (e) {
                    // If URL parsing fails, fall back to simple comparison
                    return oldUrl !== newUrl;
                }
            }
            
            // Function to fetch title from URL
            function fetchTitleFromUrl(url, formTextElement, originalTextContent) {
                // Validate URL format first
                if (!url || !url.match(/^https?:\/\/.+\..+/)) {
                    return; // Not a valid URL format, don't try to fetch
                }
                
                // Show loading indicator if formText exists
                if (formTextElement) {
                    formTextElement.innerHTML = '<i class="bi bi-arrow-repeat spinner me-1"></i> {{ _("Fetching title from URL...") }}';
                }
                
                // Add a timeout to cancel the fetch if it takes too long
                const timeoutId = setTimeout(() => {
                    if (formTextElement) {
                        formTextElement.innerHTML = '<i class="bi bi-exclamation-triangle text-warning me-1"></i> {{ _("Title fetch timed out. You can enter a name manually.") }}';
                        setTimeout(() => {
                            if (originalTextContent) formTextElement.textContent = originalTextContent;
                        }, 3000);
                    }
                }, 15000); // 15 second timeout
                
                fetch("{{ url_for('main.get_title') }}?url=" + encodeURIComponent(url))
                    .then(response => {
                        clearTimeout(timeoutId);
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update name field with the fetched title
                        nameInput.value = data.title;
                        
                        // Highlight the field with the fetched title
                        nameInput.classList.add('border-success');
                        setTimeout(() => nameInput.classList.remove('border-success'), 2000);
                        
                        // Restore original help text if formText exists
                        if (formTextElement && originalTextContent) {
                            formTextElement.textContent = originalTextContent;
                        }
                    })
                    .catch(error => {
                        clearTimeout(timeoutId);
                        console.error('Error:', error);
                        if (formTextElement) {
                            formTextElement.innerHTML = '<i class="bi bi-exclamation-triangle text-warning me-1"></i> {{ _("Could not fetch title. Please enter a name manually.") }}';
                            setTimeout(() => {
                                if (originalTextContent) formTextElement.textContent = originalTextContent;
                            }, 3000);
                        }
                    });
            }
            
            // Create a debounced version of the fetch function (wait 800ms after typing stops)
            const debouncedFetchTitle = debounce(function() {
                const url = linkInput.value;
                
                // Check if URL has been completely replaced
                if (url && isNewUrl(lastUrl, url)) {
                    urlCompletelyChanged = true;
                }
                
                // Update last URL
                lastUrl = url;
                
                // Only fetch if URL has changed from original
                if (url && url !== originalLink) {
                    fetchTitleFromUrl(url, formText, originalText);
                }
            }, 800);
            
            // Auto-fetch title as user types in the URL field if it's different from original
            linkInput.addEventListener('input', function() {
                if (this.value !== originalLink) {
                    debouncedFetchTitle();
                }
            });
            
            // Immediately fetch title when a URL is pasted
            linkInput.addEventListener('paste', function(e) {
                // Use a shorter timeout for paste events to provide faster feedback
                setTimeout(() => {
                    const url = this.value;
                    
                    // Check if URL has been completely replaced
                    if (url && isNewUrl(originalLink, url)) {
                        urlCompletelyChanged = true;
                        fetchTitleFromUrl(url, formText, originalText);
                    } else if (url && url !== originalLink) {
                        fetchTitleFromUrl(url, formText, originalText);
                    }
                }, 100); // Short delay to ensure the pasted content is available
            });

            // Form submission with loading state
            form.addEventListener('submit', function (e) {
                // Basic validation
                let isValid = true;

                if (!nameInput.value.trim()) {
                    nameInput.classList.add('is-invalid');
                    isValid = false;
                }

                if (!linkInput.value.trim()) {
                    linkInput.classList.add('is-invalid');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                    return false;
                }

                // Show loading state
                submitButton.disabled = true;
                const originalButtonText = submitButton.innerHTML;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{{ _("Saving...") }}';

                // Continue with form submission
                return true;
            });

            // Remove validation styling when user starts typing
            nameInput.addEventListener('input', function () {
                this.classList.remove('is-invalid');
            });

            linkInput.addEventListener('input', function () {
                this.classList.remove('is-invalid');
            });
        });
    </script>
    <style>
        /* Simple spinner animation */
        .spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
{% endblock %}
