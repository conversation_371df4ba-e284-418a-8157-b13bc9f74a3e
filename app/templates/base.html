<!DOCTYPE html>
<!-- Current language: {{ g.get('lang_code', 'en') }} -->
{% from 'bootstrap5/form.html' import render_form %}
{% from 'custom_macros.html' import render_flash_messages %}
<html lang="{{ g.get('lang_code', 'en') }}" data-theme="{{ g.get('theme', 'light') }}"
    data-language="{{ g.get('lang_code', 'en') }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FiestaMagic{% endblock %}</title>
    <!-- Updated Bootstrap to latest version -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons instead of Material Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='dark-mode.css') }}">
    <!-- Favicon using logo -->
    <link rel="icon" href="{{ url_for('static', filename='images/logo.svg') }}" type="image/svg+xml">
    <link rel="icon" href="{{ url_for('static', filename='images/logo-dark.svg') }}" type="image/svg+xml"
        media="(prefers-color-scheme: dark)">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/logo.svg') }}">
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <meta name="theme-color" content="#6366f1">
<!-- Logo switching based on theme -->
<style>
    /* Logo switching based on theme */
    html[data-theme="light"] .logo-light {
        display: inline-block !important;
    }

    html[data-theme="light"] .logo-dark {
        display: none !important;
    }

    html[data-theme="dark"] .logo-light {
        display: none !important;
    }

    html[data-theme="dark"] .logo-dark {
        display: inline-block !important;
    }

    /* Default state (before JS loads) */
    .logo-light {
        display: inline-block;
    }

    .logo-dark {
        display: none;
    }
</style>
    <!-- Theme Switcher Script -->
    <script src="{{ url_for('static', filename='theme-switcher.js') }}"></script>
    <!-- Language Switcher Script -->
    <script src="{{ url_for('static', filename='language-switcher.js') }}"></script>

    <!-- Custom styles -->
    <style>
        #theme-icon {
            margin-right: 0.75rem !important;
            /* Increase spacing between icon and text */
        }

        #theme-toggle {
            min-width: 100px;
            /* Ensure button has minimum width */
        }
    </style>
</head>

<body>
    <!-- Modern navbar with gradient background -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('main.index') }}">
                <img src="{{ url_for('static', filename='images/logo.svg') }}" alt="FiestaMagic Logo" width="32" height="32"
                    class="d-inline-block align-top me-2 logo-light">
                <img src="{{ url_for('static', filename='images/logo-dark.svg') }}" alt="FiestaMagic Logo" width="32" height="32"
                    class="d-inline-block align-top me-2 logo-dark">
                <span>FiestaMagic</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{{ url_for('main.index') }}">
                            <i class="bi bi-house-door me-1"></i> {{ _('Home') }}
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="{{ current_user.gravatar(size=30) }}" alt="{{ current_user.username }}"
                                class="rounded-circle me-1" width="30" height="30">
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                    <i class="bi bi-gear me-2"></i>{{ _('Profile') }}
                                </a>
                            </li>
                            {% if current_user.is_admin() %}
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin.index') }}">
                                    <i class="bi bi-shield-lock me-2"></i>{{ _('Admin Panel') }}
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>{{ _('Logout') }}
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{{ url_for('auth.login') }}">
                            <i class="bi bi-box-arrow-in-right me-1"></i> {{ _('Login') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="{{ url_for('auth.register') }}">
                            <i class="bi bi-person-plus me-1"></i> {{ _('Register') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main content wrapper -->
    <main class="flex-grow-1">
        <!-- Improved breadcrumbs with better styling -->
        <div class="container mt-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb p-3 rounded-3 shadow-sm">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}"><i class="bi bi-house"></i> {{
                            _('Home') }}</a></li>
                    {% block breadcrumbs %}{% endblock %}
                </ol>
            </nav>

            <!-- Flash messages -->
            {{ render_flash_messages() }}

            <!-- Main content with improved spacing -->
            <div class="content-container py-4 mb-5">
                {% block content %}{% endblock %}
            </div>
        </div>
    </main>

    <!-- Subtle sticky footer -->
    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0">© 2025 FiestaMagic | {{ _('All rights reserved') }}</p>
            <div class="mt-3">
                <div class="mb-2 small">
                    {{ _('Current language') }}:
                    <a href="{{ url_for('set_language', lang='en', next=request.path) }}"
                        class="{{ 'fw-bold text-primary' if g.get('lang_code') == 'en' else 'text-muted' }}">English</a> |
                    <a href="{{ url_for('set_language', lang='pl', next=request.path) }}"
                        class="{{ 'fw-bold text-primary' if g.get('lang_code') == 'pl' else 'text-muted' }}">Polski</a>
                </div>
            </div>
        </div>
        </footer>

        <!-- Updated Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Add jQuery for better DOM manipulation -->
        <script src="https://code.jquery.com/jquery-3.7.1.slim.min.js"></script>

        <!-- Custom scripts block -->
    {% block scripts %}{% endblock %}
    <!-- Common UI enhancements -->
    <script>
        // Initialize all tooltips
        document.addEventListener('DOMContentLoaded', function () {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })
        });
    </script>
</body>

</html>