:root {
    /* Enhanced gray palette with vibrant color undertones */
    --gray-100: #f3f4f8;
    /* Very light blue-gray */
    --gray-200: #e5e9f0;
    /* Light blue-gray */
    --gray-300: #d8dee9;
    /* Blue-gray */
    --gray-400: #c0c7d5;
    /* Medium blue-gray */
    --gray-500: #a3adc2;
    /* Blue-gray with more blue */
    --gray-600: #7b89a8;
    /* Muted blue */
    --gray-700: #5e6687;
    /* Deep blue-gray */
    --gray-800: #3b4252;
    /* Dark blue-gray */
    --gray-900: #2e3440;
    /* Very dark blue-gray */

    /* Light mode variables with more vibrant colors */
    --bg-color: #ffffff;
    --bg-secondary: #eef1f8;
    /* Light blue tint */
    --text-color: #2e3b52;
    /* Deeper blue-tinted text */
    --text-muted: #6d7a96;
    /* Vibrant muted blue */
    --border-color: #c2cfe0;
    /* Blue-tinted border */

    /* Dark mode variables with more vibrant colors */
    --bg-color-dark: #1c2333;
    /* Dark blue instead of black */
    --bg-secondary-dark: #2a3349;
    /* Rich navy background */
    --text-color-dark: #e4e9f2;
    /* Blue-white text */
    --text-muted-dark: #a4b1cd;
    /* Vibrant muted blue for dark mode */
    --border-color-dark: #4c5980;
    /* Enhanced blue-gray border */

    /* Alert colors */
    --warning: #fbbf24;
    --warning-dark: #f59e0b;
    --danger: #ef4444;
    --danger-dark: #dc2626;
    --success: #10b981;
    --success-dark: #059669;
    --info: #3b82f6;
    --info-dark: #2563eb;
/* Primary and secondary colors for links and buttons */
    --primary: #000000;
    --primary-dark: #ffffff;
    --secondary: #64748b;
    --secondary-dark: #94a3b8;
}

/* Apply dark mode variables */
[data-theme="dark"] {
    --bg-color: var(--bg-color-dark);
    --bg-secondary: var(--bg-secondary-dark);
    --text-color: var(--text-color-dark);
    --text-muted: var(--text-muted-dark);
    --border-color: var(--border-color-dark);

    /* Override Bootstrap components in dark mode */

    /* Card overrides */
    --bs-card-bg: var(--bg-color) !important;
    --bs-card-color: var(--text-color) !important;
    --bs-card-cap-bg: var(--bg-secondary) !important;
    --bs-card-border-color: var(--border-color) !important;

    /* Modal overrides */
    --bs-modal-bg: var(--bg-color) !important;
    --bs-modal-color: var(--text-color) !important;
    --bs-modal-header-border-color: var(--border-color) !important;
    --bs-modal-footer-border-color: var(--border-color) !important;

    /* Form overrides */
    --bs-form-control-bg: var(--bg-color) !important;
    --bs-form-control-color: var(--text-color) !important;
    --bs-form-control-border-color: var(--border-color) !important;
    --bs-input-group-addon-bg: var(--bg-secondary) !important;
    --bs-input-group-addon-color: var(--text-color) !important;
    --bs-input-group-addon-border-color: var(--border-color) !important;

    /* Text overrides */
    --bs-body-color: var(--text-color) !important;
    --bs-body-bg: var(--bg-color) !important;
    --bs-secondary-color: var(--text-muted) !important;
}

/* Direct element overrides for dark mode */
[data-theme="dark"] .card {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .card-body {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .card-footer {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .modal-content {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

/* Special handling for colored modal headers */
[data-theme="dark"] .modal-header.bg-danger {
    background-color: var(--danger) !important;
    color: white !important;
}

[data-theme="dark"] .modal-header.bg-danger .modal-title {
    color: white !important;
}

[data-theme="dark"] .btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}
[data-theme="dark"] .form-control {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .text-muted,
[data-theme="dark"] section.bg-white .text-muted,
[data-theme="dark"] p.text-muted {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Fix for transparent backgrounds */
[data-theme="dark"] .bg-transparent {
    background-color: transparent !important;
}

/* Fix for light backgrounds in dark mode */
[data-theme="dark"] .bg-light {
    background-color: var(--bg-secondary-dark) !important;
    color: var(--text-color-dark) !important;
}
/* Fix for white backgrounds in dark mode */
[data-theme="dark"] .bg-white,
[data-theme="dark"] section.bg-white,
[data-theme="dark"] div.bg-white {
    background-color: var(--bg-color-dark) !important;
    color: var(--text-color-dark) !important;
}
/* Fix for list group items */
[data-theme="dark"] .list-group-item {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

/* Fix for alerts and warning backgrounds */
[data-theme="dark"] .alert-warning,
[data-theme="dark"] .bg-warning.bg-opacity-10 {
    background-color: rgba(251, 191, 36, 0.1) !important;
    color: var(--warning-dark) !important;
    border-color: rgba(251, 191, 36, 0.2) !important;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(251, 191, 36, 0.05) !important;
    border-left: 4px solid var(--warning) !important;
    color: var(--text-color-dark) !important;
    border-radius: 0.25rem !important;
    padding: 1rem !important;
    border-color: var(--border-color-dark) !important;
}

[data-theme="dark"] .alert {
    background-color: var(--bg-color-dark) !important;
    border: 1px solid var(--border-color-dark) !important;
    color: var(--text-color-dark) !important;
}

[data-theme="dark"] .alert-warning .bi-exclamation-triangle-fill,
[data-theme="dark"] .bi-exclamation-triangle-fill {
    color: var(--warning) !important;
}
[data-theme="dark"] .text-warning {
    color: var(--warning) !important;
}

/* Fix for badges */
[data-theme="dark"] .badge.bg-success {
    background-color: var(--success) !important;
}

[data-theme="dark"] .badge.bg-primary {
    background-color: var(--primary-dark) !important;
}

[data-theme="dark"] .badge.bg-secondary {
    background-color: var(--secondary-dark) !important;
}

[data-theme="dark"] .badge.bg-danger {
    background-color: var(--danger) !important;
}

[data-theme="dark"] .badge.bg-warning {
    background-color: var(--warning) !important;
    color: #000 !important;
}

[data-theme="dark"] .badge.bg-info {
    background-color: var(--info) !important;
}
/* Fix for input placeholders */
[data-theme="dark"] .form-control::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.7;
}

/* Fix for form labels and text */
[data-theme="dark"] .form-label,
[data-theme="dark"] .form-text {
    color: var(--text-color) !important;
}

/* Fix for input groups */
[data-theme="dark"] .input-group-text {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

/* Fix for tables */
[data-theme="dark"] .table {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .table thead th {
    color: var(--text-color) !important;
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .table tbody tr {
    color: var(--text-color) !important;
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .table-responsive {
    border-color: var(--border-color) !important;
}

/* Fix for dropdowns */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color) !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .dropdown-divider {
    border-color: var(--border-color) !important;
}

/* Fix for pagination */
[data-theme="dark"] .page-link {
    background-color: var(--bg-color) !important;
    color: var(--primary-dark) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .page-link:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--primary-dark) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .page-item.active .page-link {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
}

[data-theme="dark"] .page-item.disabled .page-link {
    background-color: var(--bg-color) !important;
    color: var(--text-muted) !important;
    border-color: var(--border-color) !important;
}

/* Fix for breadcrumbs */
[data-theme="dark"] .breadcrumb {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .breadcrumb-item a {
    color: var(--primary-dark) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
    color: var(--text-color) !important;
}

/* Fix for navbar */
[data-theme="dark"] .navbar,
[data-theme="dark"] .navbar-light,
[data-theme="dark"] .bg-light {
    background-color: var(--bg-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .navbar-brand,
[data-theme="dark"] .navbar-nav .nav-link {
    color: var(--text-color) !important;
}

[data-theme="dark"] .navbar-nav .nav-link:hover,
[data-theme="dark"] .navbar-nav .nav-link:focus {
    color: var(--primary-dark) !important;
}

[data-theme="dark"] .navbar-toggler {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .navbar-toggler-icon {
    filter: invert(1);
}

/* Fix for footer */
[data-theme="dark"] footer {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] footer a {
    color: var(--text-color) !important;
}

[data-theme="dark"] footer a:hover {
    color: var(--primary-dark) !important;
}

/* Fix for border-top and border-bottom */
[data-theme="dark"] .border-top {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-bottom {
    border-color: var(--border-color) !important;
}
/* Fix for headings */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: var(--text-color) !important;
    margin-bottom: 1rem !important;
    padding: 0.5rem 0 !important;
}

/* Fix for card titles and modal titles */
[data-theme="dark"] .card-title,
[data-theme="dark"] .modal-title {
    margin-bottom: 0.75rem !important;
    padding: 0.25rem 0 !important;
}

/* Add proper spacing to content containers */
[data-theme="dark"] .card-body,
[data-theme="dark"] .modal-body {
    padding: 1.5rem !important;
}

/* Fix for links */
[data-theme="dark"] a {
    color: var(--primary-dark) !important;
}

[data-theme="dark"] a:hover {
    color: var(--secondary-dark) !important;
}

/* Fix for buttons */
[data-theme="dark"] .btn-outline-secondary {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .btn-outline-danger {
    color: var(--danger) !important;
    border-color: var(--danger) !important;
}

[data-theme="dark"] .btn-outline-danger:hover {
    background-color: var(--danger) !important;
    color: white !important;
}

[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-dark) !important;
    color: white !important;
}

/* Fix for text colors */
[data-theme="dark"] .text-decoration-none {
    color: var(--text-color) !important;
}

[data-theme="dark"] .text-decoration-none:hover {
    color: var(--primary-dark) !important;
}

/* Fix for small text elements */
[data-theme="dark"] small,
[data-theme="dark"] .small {
    color: var(--text-muted) !important;
}
/* Fix for SVG images in dark mode */
[data-theme="dark"] .card img[src$=".svg"] {
    filter: brightness(0.9) contrast(1.1);
}

/* Hero image in dark mode */
[data-theme="dark"] .hero-image-container {
    background-color: var(--bg-secondary-dark);
    border-radius: 1rem;
    padding: 1.5rem;
}

[data-theme="dark"] .hero-image-container img[src$=".svg"] {
    filter: brightness(0.9) contrast(1.1) invert(0.8);
}
/* Fix for logo switching in dark mode */
[data-theme="dark"] .logo-light {
    display: none !important;
}

[data-theme="dark"] .logo-dark {
    display: inline !important;
}

/* Hide dark logo in light mode */
[data-theme="light"] .logo-dark,
.logo-dark {
    display: none !important;
}

[data-theme="light"] .logo-light,
.logo-light {
    display: inline !important;
}

/* Fix button hover states for light mode */
.btn-primary:hover,
.btn-outline-primary:hover,
.btn-outline-secondary:hover,
.btn-outline-danger:hover,
.btn-outline-warning:hover,
.btn-outline-success:hover,
.btn-outline-info:hover {
    background-color: var(--gray-400) !important;
    color: var(--text-color) !important;
}

/* Fix dropdown hover states for light mode */
.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--gray-400) !important;
    color: var(--text-color) !important;
}

/* Fix card title colors for light mode */
[data-theme="light"] .card-title,
.card-title {
    color: var(--text-color) !important;
}

/* Fix table text colors for light mode */
[data-theme="light"] .table,
[data-theme="light"] .table th,
[data-theme="light"] .table td {
    color: var(--text-color) !important;
}

/* Fix link colors in tables for light mode */
[data-theme="light"] .table a,
[data-theme="light"] .text-decoration-none {
    color: var(--primary) !important;
}

[data-theme="light"] .table a:hover,
[data-theme="light"] .text-decoration-none:hover {
    color: var(--text-color) !important;
}

/* Fix navbar colors for light mode */
[data-theme="light"] .navbar-brand,
[data-theme="light"] .navbar-nav .nav-link {
    color: var(--text-color) !important;
}

[data-theme="light"] .navbar-nav .nav-link:hover,
[data-theme="light"] .navbar-nav .nav-link:focus {
    color: var(--primary) !important;
}

/* Fix breadcrumb colors for light mode */
[data-theme="light"] .breadcrumb-item,
[data-theme="light"] .breadcrumb-item.active {
    color: var(--text-color) !important;
}

[data-theme="light"] .breadcrumb-item a {
    color: var(--primary) !important;
}

[data-theme="light"] .breadcrumb-item a:hover {
    color: var(--text-color) !important;
}

/* Ensure dark mode buttons still work correctly */
[data-theme="dark"] .btn-primary:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-dark) !important;
    color: var(--bg-color-dark) !important;
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .btn-outline-danger:hover {
    background-color: var(--danger) !important;
    color: white !important;
}
/* Fix for landing page section */
[data-theme="dark"] section.py-5.bg-white,
[data-theme="dark"] section.bg-white {
    background-color: var(--bg-color-dark) !important;
    color: var(--text-color-dark) !important;
}

/* Fix for headings in dark mode */
[data-theme="dark"] .display-4,
[data-theme="dark"] h1.display-4,
[data-theme="dark"] section.bg-white h1,
[data-theme="dark"] section.bg-white .lead {
    color: var(--text-color-dark) !important;
}
/* Fix for card headers - add more padding (for both light and dark modes) */
.card-header {
    padding: 1rem 1.25rem !important;
}

/* Ensure proper spacing for elements inside card headers (for both light and dark modes) */
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6,
.card-header .card-title {
    margin-bottom: 0 !important;
    padding: 0.25rem 0 !important;
}

/* Keep the dark mode specific overrides as well */
[data-theme="dark"] .card-header {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

/* Improve general padding and spacing for all content containers */
.card-body,
.modal-body,
.content-container {
    padding: 1.5rem !important;
}

/* Add more breathing room to card headers */
.card-header {
    padding: 1rem 1.25rem !important;
}

/* Add more padding to card footers */
.card-footer {
    padding: 1rem 1.25rem !important;
}

/* Improve spacing for buttons and controls in containers */
.card-body .btn,
.modal-body .btn,
.content-container .btn {
    margin: 0.25rem !important;
}

/* Add more spacing between form elements */
.form-group,
.mb-3 {
    margin-bottom: 1.25rem !important;
}

/* Ensure proper spacing for headings in all containers */
.card-body h1,
.card-body h2,
.card-body h3,
.card-body h4,
.card-body h5,
.card-body h6,
.modal-body h1,
.modal-body h2,
.modal-body h3,
.modal-body h4,
.modal-body h5,
.modal-body h6,
.content-container h1,
.content-container h2,
.content-container h3,
.content-container h4,
.content-container h5,
.content-container h6 {
    margin-top: 0.5rem !important;
    margin-bottom: 1rem !important;
}
/* Mobile-friendly adjustments */
@media (max-width: 576px) {

    /* Reduce base font size for mobile */
    body {
        font-size: 0.875rem !important;
    }

    /* Smaller paragraph text */
    p {
        font-size: 0.875rem !important;
        margin-bottom: 0.75rem !important;
    }

    /* Smaller text in various components */
    .card-text,
    .modal-body,
    .form-text,
    .text-muted,
    .small {
        font-size: 0.8rem !important;
    }

    /* Smaller form labels */
    .form-label {
        font-size: 0.85rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Smaller form controls */
    .form-control,
    .form-select,
    .input-group-text {
        font-size: 0.85rem !important;
        padding: 0.375rem 0.5rem !important;
    }

    /* Smaller card titles */
    .card-title {
        font-size: 1.1rem !important;
        margin-bottom: 0.5rem !important;
    }

    /* Smaller nav links */
    .nav-link,
    .navbar-nav .nav-link {
        font-size: 0.85rem !important;
        padding: 0.4rem 0.75rem !important;
    }

    /* Smaller dropdown items */
    .dropdown-item {
        font-size: 0.85rem !important;
    }

    /* Reduce padding for content containers */
    .card-body,
    .modal-body,
    .content-container {
        padding: 0.75rem !important;
    }

    /* Smaller buttons on mobile */
    .btn {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Reduce card header/footer padding */
    .card-header,
    .card-footer {
        padding: 0.75rem 1rem !important;
    }

    /* Smaller headings */
    h1,
    .h1 {
        font-size: 1.75rem !important;
    }

    h2,
    .h2 {
        font-size: 1.5rem !important;
    }

    h3,
    .h3 {
        font-size: 1.25rem !important;
    }

    h4,
    .h4 {
        font-size: 1.125rem !important;
    }

    /* Reduce form spacing */
    .form-group,
    .mb-3 {
        margin-bottom: 0.75rem !important;
    }

    /* Tighter list groups */
    .list-group-item {
        padding: 0.5rem 0.75rem !important;
    }

    /* Smaller breadcrumbs */
    .breadcrumb {
        padding: 0.5rem 0.75rem !important;
        margin-bottom: 0.75rem !important;
    }

    /* Make action buttons more compact */
    .d-flex.flex-wrap.gap-2 .btn,
    .d-flex.align-items-center.gap-2 .btn,
    .btn-group .btn-sm {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
    }

    /* Reduce gap between action buttons */
    .d-flex.flex-wrap.gap-2,
    .d-flex.align-items-center.gap-2 {
        gap: 0.25rem !important;
    }

    /* Make dropdown menu items more compact */
    .dropdown-item {
        padding: 0.4rem 1rem !important;
        font-size: 0.875rem !important;
    }

    /* Smaller icons in buttons */
    .btn .bi {
        font-size: 0.875rem !important;
    }

    /* Adjust button groups to be more compact */
    .btn-group {
        margin: 0.125rem !important;
    }

    /* Make list items more compact */
    .list-group-item {
        padding: 0.5rem 0.75rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Reduce spacing in list item content */
    .list-group-item h5,
    .list-group-item .mb-1 {
        margin-bottom: 0.25rem !important;
        font-size: 0.95rem !important;
    }

    /* Make description text smaller */
    .list-group-item p,
    .list-group-item .text-muted {
        font-size: 0.8rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Adjust spacing for gift list items */
    .list-group-item.list-group-item-action {
        padding: 0.5rem !important;
    }

    /* Make badges smaller */
    .badge {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.7rem !important;
    }

    /* Reduce spacing between list item elements */
    .list-group-item .d-flex {
        gap: 0.5rem !important;
    }

    /* Fix action buttons positioning in list items */
    .list-group-item.d-flex {
        flex-wrap: wrap !important;
    }

    /* Adjust the flex layout for list items with actions */
    .list-group-item .d-flex.justify-content-between {
        flex-wrap: wrap !important;
    }

    /* Give more space to the content and less to actions */
    .list-group-item .d-flex.align-items-center.flex-grow-1 {
        flex: 1 1 100% !important;
        margin-bottom: 0.5rem !important;
    }

    /* Make action buttons container take full width if needed */
    .list-group-item .d-flex.align-items-center.gap-2 {
        flex: 1 1 100% !important;
        justify-content: flex-end !important;
    }

    /* Ensure buttons stay within container bounds */
    .list-group-item .btn-group,
    .list-group-item .reservation-form {
        flex-shrink: 0 !important;
    }
}